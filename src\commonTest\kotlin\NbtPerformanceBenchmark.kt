import cn.altawk.nbt.NbtFormat
import cn.altawk.nbt.tag.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.*
import kotlin.test.*
import kotlin.time.measureTime
import kotlin.time.Duration

/**
 * Performance benchmark tests for NBT encoding/decoding
 * These tests measure and compare performance characteristics
 */
class NbtPerformanceBenchmark {

    private val format = NbtFormat{
    }

    @Serializable
    data class BenchmarkData(
        val id: String,
        val timestamp: Long,
        val values: List<Double>,
        val metadata: Map<String, String>,
        val flags: List<Boolean>,
        val coordinates: List<Triple<Double, Double, Double>>
    )

    @Serializable
    data class MinecraftLikeData(
        val playerName: String,
        val level: Int,
        val health: Float,
        val inventory: List<ItemStack>,
        val position: Position,
        val gameMode: String,
        val achievements: Map<String, Boolean>
    )

    @Serializable
    data class ItemStack(
        val id: String,
        val count: Int,
        val damage: Short,
        val enchantments: Map<String, Int>
    )

    @Serializable
    data class Position(
        val x: Double,
        val y: Double,
        val z: Double,
        val yaw: Float,
        val pitch: Float
    )

    private fun createBenchmarkData(size: Int): BenchmarkData {
        return BenchmarkData(
            id = "benchmark_$size",
            timestamp = System.currentTimeMillis(),
            values = (1..size).map { it * 0.1 },
            metadata = (1..size/10).associate { "key_$it" to "value_$it" },
            flags = (1..size/5).map { it % 2 == 0 },
            coordinates = (1..size/20).map { Triple(it.toDouble(), it * 2.0, it * 3.0) }
        )
    }

    private fun createMinecraftLikeData(): MinecraftLikeData {
        return MinecraftLikeData(
            playerName = "TestPlayer",
            level = 42,
            health = 20.0f,
            inventory = (1..36).map { slot ->
                ItemStack(
                    id = "minecraft:item_$slot",
                    count = slot % 64 + 1,
                    damage = (slot % 100).toShort(),
                    enchantments = mapOf(
                        "sharpness" to slot % 5 + 1,
                        "unbreaking" to slot % 3 + 1
                    )
                )
            },
            position = Position(
                x = 123.456,
                y = 64.0,
                z = -789.012,
                yaw = 45.0f,
                pitch = -30.0f
            ),
            gameMode = "survival",
            achievements = (1..50).associate { "achievement_$it" to (it % 2 == 0) }
        )
    }

    private fun createComplexNbtStructure(): NbtCompound {
        return NbtCompound {
            put("version", "1.0.0")
            put("timestamp", System.currentTimeMillis())
            
            putList("players") {
                repeat(10) { i ->
                    addCompound {
                        put("name", "Player$i")
                        put("level", i * 10)
                        put("health", 20.0f)
                        put("experience", i * 1000.0)
                        
                        putList("inventory") {
                            repeat(27) { slot ->
                                addCompound {
                                    put("slot", slot)
                                    put("id", "item_${slot % 10}")
                                    put("count", slot % 64 + 1)
                                    put("damage", (slot % 100).toShort())
                                }
                            }
                        }
                        
                        putCompound("position") {
                            put("x", i * 100.0)
                            put("y", 64.0 + i)
                            put("z", i * -50.0)
                            put("dimension", "overworld")
                        }
                        
                        put("stats", intArrayOf(i, i*2, i*3, i*4, i*5))
                        put("uuid", byteArrayOf(
                            i.toByte(), (i+1).toByte(), (i+2).toByte(), (i+3).toByte(),
                            (i+4).toByte(), (i+5).toByte(), (i+6).toByte(), (i+7).toByte(),
                            (i+8).toByte(), (i+9).toByte(), (i+10).toByte(), (i+11).toByte(),
                            (i+12).toByte(), (i+13).toByte(), (i+14).toByte(), (i+15).toByte()
                        ))
                    }
                }
            }
            
            putCompound("world_data") {
                put("seed", 123456789L)
                put("spawn_x", 0)
                put("spawn_y", 64)
                put("spawn_z", 0)
                put("time", 24000L)
                put("weather", "clear")
                
                putList("loaded_chunks") {
                    repeat(100) { i ->
                        addCompound {
                            put("x", i % 10)
                            put("z", i / 10)
                            put("last_update", System.currentTimeMillis() - i * 1000)
                        }
                    }
                }
            }
        }
    }

    @Test
    fun benchmarkSmallDataSerialization() {
        val data = createBenchmarkData(100)
        val iterations = 1000
        
        println("\n=== Small Data Benchmark (100 elements, $iterations iterations) ===")
        
        // TreeNbt encoding
        val treeEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToNbtTag(BenchmarkData.serializer(), data)
            }
        }
        
        // TreeNbt decoding
        val nbtTag = format.encodeToNbtTag(BenchmarkData.serializer(), data)
        val treeDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromNbtTag(BenchmarkData.serializer(), nbtTag)
            }
        }
        
        // SNBT encoding
        val snbtEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToString(BenchmarkData.serializer(), data)
            }
        }
        
        // SNBT decoding
        val snbtString = format.encodeToString(BenchmarkData.serializer(), data)
        val snbtDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromString(BenchmarkData.serializer(), snbtString)
            }
        }
        
        printBenchmarkResults("Small Data", iterations, treeEncodeTime, treeDecodeTime, snbtEncodeTime, snbtDecodeTime)
    }

    @Test
    fun benchmarkMediumDataSerialization() {
        val data = createBenchmarkData(1000)
        val iterations = 100
        
        println("\n=== Medium Data Benchmark (1000 elements, $iterations iterations) ===")
        
        val treeEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToNbtTag(BenchmarkData.serializer(), data)
            }
        }
        
        val nbtTag = format.encodeToNbtTag(BenchmarkData.serializer(), data)
        val treeDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromNbtTag(BenchmarkData.serializer(), nbtTag)
            }
        }
        
        val snbtEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToString(BenchmarkData.serializer(), data)
            }
        }
        
        val snbtString = format.encodeToString(BenchmarkData.serializer(), data)
        val snbtDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromString(BenchmarkData.serializer(), snbtString)
            }
        }
        
        printBenchmarkResults("Medium Data", iterations, treeEncodeTime, treeDecodeTime, snbtEncodeTime, snbtDecodeTime)
    }

    @Test
    fun benchmarkLargeDataSerialization() {
        val data = createBenchmarkData(10000)
        val iterations = 10
        
        println("\n=== Large Data Benchmark (10000 elements, $iterations iterations) ===")
        
        val treeEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToNbtTag(BenchmarkData.serializer(), data)
            }
        }
        
        val nbtTag = format.encodeToNbtTag(BenchmarkData.serializer(), data)
        val treeDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromNbtTag(BenchmarkData.serializer(), nbtTag)
            }
        }
        
        val snbtEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToString(BenchmarkData.serializer(), data)
            }
        }
        
        val snbtString = format.encodeToString(BenchmarkData.serializer(), data)
        val snbtDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromString(BenchmarkData.serializer(), snbtString)
            }
        }
        
        printBenchmarkResults("Large Data", iterations, treeEncodeTime, treeDecodeTime, snbtEncodeTime, snbtDecodeTime)
    }

    @Test
    fun benchmarkMinecraftLikeData() {
        val data = createMinecraftLikeData()
        val iterations = 500
        
        println("\n=== Minecraft-like Data Benchmark ($iterations iterations) ===")
        
        val treeEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToNbtTag(MinecraftLikeData.serializer(), data)
            }
        }
        
        val nbtTag = format.encodeToNbtTag(MinecraftLikeData.serializer(), data)
        val treeDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromNbtTag(MinecraftLikeData.serializer(), nbtTag)
            }
        }
        
        val snbtEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToString(MinecraftLikeData.serializer(), data)
            }
        }
        
        val snbtString = format.encodeToString(MinecraftLikeData.serializer(), data)
        val snbtDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromString(MinecraftLikeData.serializer(), snbtString)
            }
        }
        
        printBenchmarkResults("Minecraft-like", iterations, treeEncodeTime, treeDecodeTime, snbtEncodeTime, snbtDecodeTime)
    }

    @Test
    fun benchmarkComplexNbtStructure() {
        val nbtData = createComplexNbtStructure()
        val iterations = 100
        
        println("\n=== Complex NBT Structure Benchmark ($iterations iterations) ===")
        
        val treeEncodeTime = measureTime {
            repeat(iterations) {
                format.encodeToNbtTag(NbtTag.serializer(), nbtData)
            }
        }
        
        val treeDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromNbtTag(NbtTag.serializer(), nbtData)
            }
        }
        
        val snbtEncodeTime = measureTime {
            repeat(iterations) {
                nbtData.toString()
            }
        }
        
        val snbtString = nbtData.toString()
        val snbtDecodeTime = measureTime {
            repeat(iterations) {
                format.decodeFromString(NbtTag.serializer(), snbtString)
            }
        }
        
        printBenchmarkResults("Complex NBT", iterations, treeEncodeTime, treeDecodeTime, snbtEncodeTime, snbtDecodeTime)
    }

    private fun printBenchmarkResults(
        testName: String,
        iterations: Int,
        treeEncodeTime: Duration,
        treeDecodeTime: Duration,
        snbtEncodeTime: Duration,
        snbtDecodeTime: Duration
    ) {
        println("$testName Results:")
        println("  TreeNbt Encode: $treeEncodeTime (${treeEncodeTime / iterations} per op)")
        println("  TreeNbt Decode: $treeDecodeTime (${treeDecodeTime / iterations} per op)")
        println("  SNBT Encode:    $snbtEncodeTime (${snbtEncodeTime / iterations} per op)")
        println("  SNBT Decode:    $snbtDecodeTime (${snbtDecodeTime / iterations} per op)")
        println("  TreeNbt vs SNBT Encode Ratio: ${snbtEncodeTime.inWholeNanoseconds.toDouble() / treeEncodeTime.inWholeNanoseconds}")
        println("  TreeNbt vs SNBT Decode Ratio: ${snbtDecodeTime.inWholeNanoseconds.toDouble() / treeDecodeTime.inWholeNanoseconds}")
    }
}
