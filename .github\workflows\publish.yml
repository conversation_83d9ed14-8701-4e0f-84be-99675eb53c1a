name: Publish

on:
  workflow_dispatch:
  release:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v4
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '8'
          distribution: 'temurin'
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
      - name: Make Gradle Wrapper Executable
        run: chmod +x ./gradlew
      - name: Build And Publish
        run: ./gradlew clean build publish
        env:
          GITHUB_USER: ${{ github.actor }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
